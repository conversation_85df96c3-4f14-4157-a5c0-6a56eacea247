# app/services/workflow_service.py
import grpc
import json
import structlog
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from app.db.session import SessionLocal, get_db
from app.models.workflow import Workflow, WorkflowVersion
from app.grpc_ import workflow_pb2, workflow_pb2_grpc
from app.utils.constants.constants import WorkflowVisibilityEnum
from app.utils.kafka.kafka_service import KafkaProducer
from app.helpers.workflow_to_protobuf import _workflow_version_to_protobuf
from app.helpers.helpers import _create_new_version_from_workflow , _create_marketplace_listing_from_workflow

# Initialize structured logger
logger = structlog.get_logger()


class WorkflowVersionFunctions(workflow_pb2_grpc.WorkflowServiceServicer):
    def __init__(self):
        """Initialize the WorkflowVersionFunctions with a KafkaProducer instance"""
        self.kafka_producer = KafkaProducer()

    def get_db(self) -> Session:
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def listWorkflowVersions(
        self, request: workflow_pb2.ListWorkflowVersionsRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.ListWorkflowVersionsResponse:
        """
        List all versions of a workflow with permission checking.
        Supports both workflow owners and marketplace users.
        """
        db = self.get_db()
        logger.info(
            "list_workflow_versions_request",
            workflow_id=request.workflow_id,
            user_id=request.user_id,
            page=request.page,
            page_size=request.page_size,
        )

        try:
            # First, check if the workflow exists
            workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()
            if not workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Workflow with ID {request.workflow_id} not found")
                return workflow_pb2.ListWorkflowVersionsResponse(
                    success=False,
                    message="Workflow not found",
                    versions=[],
                    total=0,
                    page=request.page,
                    total_pages=0,
                )

            # Permission check: Owner can see all versions, marketplace users can only see public workflow versions
            is_owner = workflow.owner_id == request.user_id if request.user_id else False
            is_public_workflow = workflow.visibility == WorkflowVisibilityEnum.PUBLIC

            # Allow access if user is owner OR workflow is public
            if not is_owner and not is_public_workflow:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details(
                    "You don't have permission to view versions of this private workflow"
                )
                return workflow_pb2.ListWorkflowVersionsResponse(
                    success=False,
                    message="Permission denied: Cannot view versions of private workflow",
                    versions=[],
                    total=0,
                    page=request.page,
                    total_pages=0,
                )

            # Query versions for the workflow
            query = db.query(WorkflowVersion).filter(
                WorkflowVersion.workflow_id == request.workflow_id
            )

            # Get total count
            total = query.count()

            # Apply pagination
            page = request.page if request.page > 0 else 1
            page_size = request.page_size if request.page_size > 0 else 10
            offset = (page - 1) * page_size

            # Order by created_at desc, then by id desc for consistent ordering
            versions = (
                query.order_by(WorkflowVersion.created_at.desc(), WorkflowVersion.id.desc())
                .offset(offset)
                .limit(page_size)
                .all()
            )

            # Calculate total pages
            total_pages = (total + page_size - 1) // page_size

            # Convert to protobuf
            version_protos = []
            for version in versions:
                is_current = version.id == workflow.current_version_id
                version_proto = _workflow_version_to_protobuf(version, is_current)
                version_protos.append(version_proto)

            logger.info(
                "workflow_versions_retrieved",
                workflow_id=request.workflow_id,
                total=total,
                page=page,
                total_pages=total_pages
            )

            return workflow_pb2.ListWorkflowVersionsResponse(
                success=True,
                message=f"Retrieved {len(version_protos)} versions for workflow",
                versions=version_protos,
                total=total,
                page=page,
                total_pages=total_pages,
                current_version_id=workflow.current_version_id or "",
            )

        except Exception as e:
            logger.error(f"Failed to list workflow versions: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to list workflow versions: {str(e)}")
            return workflow_pb2.ListWorkflowVersionsResponse(
                success=False,
                message=f"Failed to list workflow versions: {str(e)}",
                versions=[],
                total=0,
                page=request.page,
                total_pages=0,
            )
        finally:
            db.close()

    def getWorkflowVersion(
        self, request: workflow_pb2.GetWorkflowVersionRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.GetWorkflowVersionResponse:
        """
        Get details of a specific workflow version with permission checking.
        Supports both workflow owners and marketplace users.
        """
        db = self.get_db()
        logger.info(
            "get_workflow_version_request",
            workflow_id=request.workflow_id,
            version_id=request.version_id,
            user_id=request.user_id,
        )

        try:
            # First, check if the workflow exists
            workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()
            if not workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Workflow with ID {request.workflow_id} not found")
                return workflow_pb2.GetWorkflowVersionResponse(
                    success=False,
                    message="Workflow not found",
                )

            # Permission check: Owner can see all versions, marketplace users can only see public workflow versions
            is_owner = workflow.owner_id == request.user_id if request.user_id else False
            is_public_workflow = workflow.visibility == WorkflowVisibilityEnum.PUBLIC

            if not is_owner and not is_public_workflow:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details(
                    "You don't have permission to view versions of this private workflow"
                )
                return workflow_pb2.GetWorkflowVersionResponse(
                    success=False,
                    message="Permission denied: Cannot view versions of private workflow",
                )

            # Get the specific version
            version = (
                db.query(WorkflowVersion)
                .filter(
                    WorkflowVersion.workflow_id == request.workflow_id,
                    WorkflowVersion.id == request.version_id,
                )
                .first()
            )

            if not version:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(
                    f"Version with ID {request.version_id} not found for workflow {request.workflow_id}"
                )
                return workflow_pb2.GetWorkflowVersionResponse(
                    success=False,
                    message="Version not found",
                )

            # Convert to protobuf
            is_current = version.id == workflow.current_version_id
            version_proto = _workflow_version_to_protobuf(version, is_current)

            logger.info(
                "workflow_version_retrieved",
                workflow_id=request.workflow_id,
                version_id=request.version_id,
                is_owner=is_owner,
                is_current=is_current,
            )

            return workflow_pb2.GetWorkflowVersionResponse(
                success=True,
                message=f"Retrieved version {version.version_number}",
                version=version_proto,
            )

        except Exception as e:
            logger.error(f"Failed to get workflow version: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to get workflow version: {str(e)}")
            return workflow_pb2.GetWorkflowVersionResponse(
                success=False,
                message=f"Failed to get workflow version: {str(e)}",
            )
        finally:
            db.close()

    def switchWorkflowVersion(
        self, request: workflow_pb2.SwitchWorkflowVersionRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.SwitchWorkflowVersionResponse:
        """
        Switch the current version of a workflow. Only owners can switch versions.
        """
        db = self.get_db()
        logger.info(
            "switch_workflow_version_request",
            workflow_id=request.workflow_id,
            version_id=request.version_id,
            user_id=request.user_id,
        )

        try:
            # First, check if the workflow exists
            workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()
            if not workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Workflow with ID {request.workflow_id} not found")
                return workflow_pb2.SwitchWorkflowVersionResponse(
                    success=False,
                    message="Workflow not found",
                )

            # Permission check: Only owners can switch versions
            if workflow.owner_id != request.user_id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Only the workflow owner can switch versions")
                return workflow_pb2.SwitchWorkflowVersionResponse(
                    success=False,
                    message="Permission denied: Only the workflow owner can switch versions",
                )

            # Check if the target version exists
            target_version = (
                db.query(WorkflowVersion)
                .filter(
                    WorkflowVersion.workflow_id == request.workflow_id,
                    WorkflowVersion.id == request.version_id,
                )
                .first()
            )

            if not target_version:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(
                    f"Version with ID {request.version_id} not found for workflow {request.workflow_id}"
                )
                return workflow_pb2.SwitchWorkflowVersionResponse(
                    success=False,
                    message="Target version not found",
                )

            # Check if it's already the current version
            if workflow.current_version_id == request.version_id:
                return workflow_pb2.SwitchWorkflowVersionResponse(
                    success=True,
                    message=f"Version {target_version.version_number} is already the current version",
                    new_current_version=_workflow_version_to_protobuf(target_version, True),
                )

            # Update the current version
            old_version_id = workflow.current_version_id
            workflow.current_version_id = request.version_id

            # Update workflow fields from the target version
            workflow.name = target_version.name
            workflow.description = target_version.description
            workflow.workflow_url = target_version.workflow_url
            workflow.builder_url = target_version.builder_url
            workflow.start_nodes = target_version.start_nodes
            workflow.category = target_version.category
            workflow.tags = target_version.tags

            db.commit()
            db.refresh(workflow)
            db.refresh(target_version)

            logger.info(
                "workflow_version_switched",
                workflow_id=request.workflow_id,
                old_version_id=old_version_id,
                new_version_id=request.version_id,
                user_id=request.user_id,
            )

            return workflow_pb2.SwitchWorkflowVersionResponse(
                success=True,
                message=f"Successfully switched to version {target_version.version_number}",
                new_current_version=_workflow_version_to_protobuf(target_version, True),
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Failed to switch workflow version: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to switch workflow version: {str(e)}")
            return workflow_pb2.SwitchWorkflowVersionResponse(
                success=False,
                message=f"Failed to switch workflow version: {str(e)}",
            )
        finally:
            db.close()

    def createVersionAndPublish(
        self, request: workflow_pb2.CreateVersionAndPublishRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.CreateVersionAndPublishResponse:
        """
        Create a new version from current workflow state and optionally publish to marketplace.
        This method replaces the automatic versioning behavior with user-controlled versioning.

        Args:
            request: Contains workflow_id, user_id, publish_to_marketplace flag, and optional version details
            context: gRPC context for error handling

        Returns:
            Response with success status, version details, and marketplace status
        """
        db = SessionLocal()
        logger.info(
            "create_version_and_publish_request",
            workflow_id=request.workflow_id,
            user_id=request.user_id,
            publish_to_marketplace=request.publish_to_marketplace,
        )

        try:
            # Get the workflow
            workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()
            if not workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Workflow not found")
                return workflow_pb2.CreateVersionAndPublishResponse(
                    success=False, message="Workflow not found"
                )

            # Check ownership
            if workflow.owner_id != request.user_id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Permission denied. You are not the owner of this workflow.")
                return workflow_pb2.CreateVersionAndPublishResponse(
                    success=False,
                    message="Permission denied. You are not the owner of this workflow.",
                )

            # Check if there are pending changes to version
            if not workflow.is_updated:
                return workflow_pb2.CreateVersionAndPublishResponse(
                    success=True,
                    message="No pending changes to create a new version. Workflow is already up to date.",
                    version_created=False,
                    marketplace_updated=False,
                )

            # Create new version from current workflow state
            new_version = _create_new_version_from_workflow(db, workflow)
            if not new_version:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to create new version")
                return workflow_pb2.CreateVersionAndPublishResponse(
                    success=False, message="Failed to create new version"
                )

            # Update workflow's current version
            workflow.current_version_id = new_version.id

            # Reset is_updated flag since we've created a version
            workflow.is_updated = False
            workflow.updated_at = datetime.now(timezone.utc)

            # Commit workflow changes before marketplace operations
            db.add(workflow)
            db.commit()
            db.refresh(workflow)

            marketplace_updated = False
            marketplace_listing_id = ""

            # Handle marketplace publishing if requested and workflow is public
            if (
                request.publish_to_marketplace
                and workflow.visibility == WorkflowVisibilityEnum.PUBLIC
            ):
                marketplace_listing = _create_marketplace_listing_from_workflow(db, workflow)
                if marketplace_listing:
                    # Commit marketplace listing changes
                    db.commit()
                    db.refresh(marketplace_listing)
                    marketplace_updated = True
                    marketplace_listing_id = str(marketplace_listing.id)
                    logger.info(
                        f"Updated marketplace listing {marketplace_listing.id} with new version {new_version.id}"
                    )
                else:
                    logger.warning(
                        f"Failed to update marketplace listing for workflow {workflow.id}"
                    )
            elif (
                request.publish_to_marketplace
                and workflow.visibility != WorkflowVisibilityEnum.PUBLIC
            ):
                logger.warning(
                    f"Cannot publish to marketplace: workflow {workflow.id} is not public"
                )

            # Refresh the new version
            db.refresh(new_version)

            # Update derived workflows to notify them of the new version
            from app.helpers.helpers import _update_derived_workflows_change_status
            
            try:
                _update_derived_workflows_change_status(db, workflow)
                logger.info(f"Updated derived workflows for source workflow {workflow.id}")
            except Exception as e:
                logger.warning(f"Failed to update derived workflows: {str(e)}")
                # Don't fail the entire operation if derived workflow update fails

            # Prepare response message
            message_parts = [f"Successfully created version {new_version.version_number}"]
            if marketplace_updated:
                message_parts.append("and updated marketplace listing")
            elif (
                request.publish_to_marketplace
                and workflow.visibility != WorkflowVisibilityEnum.PUBLIC
            ):
                message_parts.append("(marketplace update skipped - workflow is not public)")

            message = " ".join(message_parts) + "."

            logger.info(
                f"Successfully created version {new_version.version_number} for workflow {workflow.id}, "
                f"marketplace_updated={marketplace_updated}"
            )

            return workflow_pb2.CreateVersionAndPublishResponse(
                success=True,
                message=message,
                version_created=True,
                marketplace_updated=marketplace_updated,
                version_number=new_version.version_number,
                version_id=str(new_version.id),
                marketplace_listing_id=marketplace_listing_id,
            )

        except Exception as e:
            db.rollback()
            logger.error("create_version_and_publish_failed", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return workflow_pb2.CreateVersionAndPublishResponse(
                success=False, message=f"Failed to create version and publish: {str(e)}"
            )
        finally:
            db.close()
