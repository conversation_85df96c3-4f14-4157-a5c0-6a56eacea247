import grpc
import json
import structlog
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from app.db.session import <PERSON><PERSON><PERSON>al
from app.models.workflow import Workflow, WorkflowVersion, WorkflowMarketplaceListing
from app.utils.constants.constants import WorkflowStatusEnum, WorkflowVisibilityEnum

logger = structlog.get_logger()

def get_db(self) -> Session:
    db = SessionLocal()
    try:
        return db
    finally:
        db.close()


def _create_marketplace_listing_from_workflow(
    db,
    workflow: Workflow,
) -> WorkflowMarketplaceListing | None:
    """
    Creates or updates a marketplace listing for a workflow using its current version.
    Following the agent-service pattern, marketplace listings should use current version data,
    not main workflow data, to ensure consistency with the versioning system.
    
    If a listing for this workflow_id by this owner already exists, it updates it
    to point to the workflow's current_version_id. Otherwise, it creates a new listing.
    """
    if not workflow.owner_id:
        logger.error(f"Workflow {workflow.id} has no owner_id, cannot create marketplace listing.")
        return None

    if not workflow.current_version_id:
        logger.error(
            f"Workflow {workflow.id} has no current_version_id, cannot create marketplace listing."
        )
        return None

    # Get the current version details (following agent-service pattern)
    current_version = db.get(WorkflowVersion, workflow.current_version_id)

    if not current_version:
        logger.error(
            f"Current version {workflow.current_version_id} not found for workflow {workflow.id}"
        )
        return None

    # Check if a marketplace listing already exists for this workflow_id by this owner
    existing_listing = (
        db.query(WorkflowMarketplaceListing)
        .filter(
            WorkflowMarketplaceListing.workflow_id == workflow.id,
            WorkflowMarketplaceListing.listed_by_user_id == workflow.owner_id,
        )
        .first()
    )

    if existing_listing:
        # Update existing listing using CURRENT VERSION DATA (following agent-service pattern)
        logger.info(
            f"Updating existing marketplace listing {existing_listing.id} for workflow {workflow.id} to version {current_version.id} ({current_version.version_number})."
        )
        existing_listing.workflow_version_id = current_version.id
        existing_listing.title = current_version.name  # Use current version name
        existing_listing.description = current_version.description or "No description provided."  # Use current version description
        existing_listing.image_url = getattr(current_version, 'image_url', None)  # Use current version image
        existing_listing.category = current_version.category  # Use current version category
        existing_listing.tags = current_version.tags if current_version.tags else []  # Use current version tags
        existing_listing.status = WorkflowStatusEnum.ACTIVE
        existing_listing.visibility = WorkflowVisibilityEnum.PUBLIC
        existing_listing.updated_at = datetime.now(timezone.utc)
        db.add(existing_listing)
        logger.info(
            f"Updated existing marketplace listing {existing_listing.id} with current version data (name: '{current_version.name}')"
        )
        return existing_listing

    # Create new marketplace listing using CURRENT VERSION DATA (following agent-service pattern)
    logger.info(
        f"Creating new marketplace listing for workflow {workflow.id} version {current_version.id} ({current_version.version_number})."
    )
    new_listing = WorkflowMarketplaceListing(
        workflow_id=workflow.id,
        workflow_version_id=current_version.id,
        listed_by_user_id=workflow.owner_id,
        title=current_version.name,  # Use current version name
        description=current_version.description or "No description provided.",  # Use current version description
        image_url=getattr(current_version, 'image_url', None),  # Use current version image
        category=current_version.category,  # Use current version category
        tags=current_version.tags if current_version.tags else [],  # Use current version tags
        use_count=0,
        execution_count=0,
        average_rating=0.0,
        visibility=WorkflowVisibilityEnum.PUBLIC,
        status=WorkflowStatusEnum.ACTIVE,
    )

    db.add(new_listing)
    db.flush()  # Ensure ID is available

    logger.info(f"Created marketplace listing {new_listing.id} for workflow {workflow.id}")
    return new_listing


def _create_new_version_from_workflow(
    db,
    workflow: Workflow,
) -> WorkflowVersion | None:
    """
    Creates a new version from the current workflow state.
    Automatically increments the version number.
    """
    try:
        # Get the current version to determine the next version number
        current_version = None
        if workflow.current_version_id:
            current_version = (
                db.query(WorkflowVersion)
                .filter(WorkflowVersion.id == workflow.current_version_id)
                .first()
            )

        # Determine the next version number
        if current_version:
            # Parse current version and increment
            current_version_parts = current_version.version_number.split(".")
            if len(current_version_parts) == 3:
                major, minor, patch = map(int, current_version_parts)
                # For now, increment minor version for updates
                new_version_number = f"{major}.{minor + 1}.0"
            else:
                # Fallback if version format is unexpected
                new_version_number = "1.1.0"
        else:
            # No current version found, start with 1.1.0 (since 1.0.0 should have been created)
            new_version_number = "1.1.0"

        # Create new version with current timestamp to ensure proper ordering
        from datetime import datetime, timezone

        current_time = datetime.now(timezone.utc)

        new_version = WorkflowVersion(
            workflow_id=workflow.id,
            version_number=new_version_number,
            name=workflow.name,
            description=workflow.description,
            workflow_url=workflow.workflow_url,
            builder_url=workflow.builder_url,
            start_nodes=workflow.start_nodes if workflow.start_nodes else [],
            available_nodes=workflow.available_nodes if workflow.available_nodes else [],
            category=workflow.category,
            tags=workflow.tags if workflow.tags else [],
            changelog=f"Updated workflow - version {new_version_number}",
            created_at=current_time,  # Explicitly set creation time
        )

        db.add(new_version)
        db.flush()  # Get the ID

        logger.info(
            f"Created new version {new_version_number} for workflow {workflow.id} at {current_time}"
        )
        return new_version

    except Exception as e:
        logger.error(f"Failed to create new version for workflow {workflow.id}: {str(e)}")
        return None


def _update_derived_workflows_change_status(db: Session, source_workflow: Workflow):
    """
    Update is_updated flag for all workflows derived from the source workflow.
    This is called when the source workflow creates a new version or is updated with template-relevant changes.

    Enhanced to use version comparison for more accurate update detection.
    """
    logger.info(
        f"Starting _update_derived_workflows_change_status for source workflow {source_workflow.id}"
    )
    try:
        # Get all workflows that were cloned from this source workflow
        derived_workflows = (
            db.query(Workflow)
            .filter(
                Workflow.workflow_template_id == source_workflow.id,
                Workflow.is_imported == True,
            )
            .all()
        )

        logger.info(
            f"Found {len(derived_workflows)} derived workflows for source {source_workflow.id}"
        )

        # For each derived workflow, check if it needs updates
        for derived_workflow in derived_workflows:
            has_updates = False
            
            # Enhanced version-based update detection
            if (source_workflow.current_version_id and
                derived_workflow.source_version_id and
                derived_workflow.source_version_id != source_workflow.current_version_id):
                # Source has a newer version than what the derived workflow was based on
                has_updates = True
                logger.info(
                    f"Version-based update detected for derived workflow {derived_workflow.id}: "
                    f"source version {derived_workflow.source_version_id} -> {source_workflow.current_version_id}"
                )
            elif not derived_workflow.source_version_id:
                # Fall back to content comparison for older cloned workflows
                has_updates = _detect_workflow_changes(derived_workflow, source_workflow)
                logger.info(
                    f"Content-based update detection for derived workflow {derived_workflow.id}: {has_updates}"
                )

            if has_updates:
                derived_workflow.is_updated = True
                logger.info(
                    f"Marked derived workflow {derived_workflow.id} as having pending updates"
                )
            else:
                logger.info(
                    f"No updates needed for derived workflow {derived_workflow.id}"
                )

        db.commit()

    except Exception as e:
        logger.error(f"Failed to update derived workflows change status: {str(e)}")
        db.rollback()


def _detect_workflow_changes(derived_workflow: Workflow, source_workflow: Workflow) -> bool:
    """
    Detect if there are meaningful changes between a derived workflow and its source.

    This method uses multiple comparison strategies to determine if the source workflow
    has changes that should trigger an update notification for the derived workflow.

    Args:
        derived_workflow: The cloned workflow
        source_workflow: The source workflow

    Returns:
        True if changes are detected, False otherwise
    """
    try:
        # Strategy 1: Timestamp comparison (primary method)
        # If source was updated after the derived workflow was last synced
        # Add null safety checks for timestamps
        if (
            source_workflow.updated_at is not None
            and derived_workflow.updated_at is not None
            and source_workflow.updated_at > derived_workflow.updated_at
        ):
            logger.info(
                f"Timestamp change detected: source updated at {source_workflow.updated_at}, "
                f"derived last updated at {derived_workflow.updated_at}"
            )
            return True
        elif source_workflow.updated_at is not None and derived_workflow.updated_at is None:
            # If derived workflow has no update timestamp but source does, assume changes
            logger.info(
                f"Timestamp change detected: source has update timestamp but derived workflow doesn't"
            )
            return True

        # Strategy 2: Content comparison for key fields
        # Compare important fields that would affect the workflow functionality
        content_fields_changed = (
            derived_workflow.description != source_workflow.description
            or derived_workflow.workflow_url != source_workflow.workflow_url
            or derived_workflow.builder_url != source_workflow.builder_url
            or derived_workflow.start_nodes != source_workflow.start_nodes
            or derived_workflow.category != source_workflow.category
            or derived_workflow.tags != source_workflow.tags
        )

        if content_fields_changed:
            logger.info(f"Content changes detected between workflows")
            return True

        # Strategy 3: Version comparison (if versions are tracked)
        # This could be enhanced with semantic versioning in the future
        # Note: Version comparison is now handled through WorkflowVersion table
        # and current_version_id relationship

        # No significant changes detected
        return False

    except Exception as e:
        logger.error(f"Error detecting workflow changes: {str(e)}")
        # In case of error, assume changes exist to be safe
        return True


def _generate_content_hash(workflow: Workflow) -> str:
    """
    Generate a hash of the workflow's content for comparison purposes.

    This method creates a hash based on the workflow's key content fields
    that would affect functionality when changed.

    Args:
        workflow: The workflow to hash

    Returns:
        SHA256 hash of the workflow content
    """
    import hashlib
    import json

    try:
        # Create a dictionary of the key content fields
        content_data = {
            "description": workflow.description or "",
            "workflow_url": workflow.workflow_url or "",
            "builder_url": workflow.builder_url or "",
            "start_nodes": workflow.start_nodes or [],
            "category": workflow.category.value if workflow.category else "",
            "tags": workflow.tags or [],
        }

        # Convert to JSON string for consistent hashing
        content_json = json.dumps(content_data, sort_keys=True)

        # Generate SHA256 hash
        return hashlib.sha256(content_json.encode("utf-8")).hexdigest()

    except Exception as e:
        logger.error(f"Error generating content hash: {str(e)}")
        # Return a timestamp-based hash as fallback
        return hashlib.sha256(str(workflow.updated_at).encode("utf-8")).hexdigest()
