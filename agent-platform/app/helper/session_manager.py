import asyncio
import json
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Tu<PERSON>


from autogen_core import Can<PERSON>ationToken
from autogen_core.memory import ListMemory, MemoryContent, MemoryMimeType

from ..schemas.agent_config import AgentConfig
from ..shared.config.logging_config import get_logger
from .redis_client import RedisClient

# Get logger for this module
logger = get_logger(__name__)


class SessionManager:
    def __init__(
        self,
        redis_client: RedisClient,
        session_ttl: int = 7200,
    ):
        self.redis = redis_client
        self.session_ttl = session_ttl
        self.session_memories: Dict[str, ListMemory] = {}
        self.cancellation_tokens: Dict[str, CancellationToken] = {}
        self._lock = asyncio.Lock()  # Lock for thread safety

    async def create_session(
        self,
        agent_config: AgentConfig,
        user_id: str,
        communication_type: str,
        organization_id: Optional[str] = None,
        use_knowledge: Optional[bool] = False,
        agent_group_id: Optional[str] = None,
        variables: Optional[dict] = None,
        chat_context: Optional[List[dict]] = None,
    ) -> str:
        """Create a new session with the given agent config"""
        session_id = str(uuid.uuid4())

        try:

            # Create new memory instance for session
            async with self._lock:
                self.session_memories[session_id] = ListMemory()
                self.cancellation_tokens[session_id] = CancellationToken()

            memory = self.session_memories[session_id]
            # Update ListMemory for compatibility
            if memory and chat_context:
                for msg in chat_context:
                    await memory.add(
                        MemoryContent(
                            content=msg,
                            mime_type=MemoryMimeType.JSON,
                        )
                    )

            # Extract only essential config data to reduce storage
            essential_config = self._extract_essential_config(agent_config)

            session_data = {
                "session_id": session_id,
                "user_id": user_id,
                "agent_config": essential_config,
                "communication_type": communication_type,
                "organization_id": organization_id,
                "use_knowledge": use_knowledge,
                "agent_group_id": agent_group_id,
                "variables": variables,
                "created_at": datetime.utcnow().isoformat(),
            }

            key = f"session_data:{session_id}"
            await self.redis.set_hash(key, session_data, ttl=self.session_ttl)

            logger.info(
                "Session created",
                extra={
                    "session_id": session_id,
                    "user_id": user_id,
                    "communication_type": communication_type,
                },
            )
            return session_id

        except Exception as e:
            logger.error(
                "Failed to create session", exc_info=True, extra={"error": str(e)}
            )
            raise e

    def _extract_essential_config(self, agent_config: Any) -> Dict[str, Any]:
        """Extract only essential configuration data to reduce storage."""
        # Convert agent_config to dict if it's a Pydantic model
        config_dict = (
            agent_config.model_dump()
            if hasattr(agent_config, "model_dump")
            else agent_config
        )

        # Extract only essential fields
        essential_fields = {
            "name": config_dict.get("name"),
            "description": config_dict.get("description"),
            "agent_type": config_dict.get("agent_type"),
            "system_message": config_dict.get("system_message"),
            # Include minimal model config
            "ai_model_config": {
                "model": config_dict.get("ai_model_config", {}).get("model"),
                "api_key": config_dict.get("ai_model_config", {}).get("api_key"),
                "temperature": config_dict.get("ai_model_config", {}).get(
                    "temperature", 0.7
                ),
            },
            # Include tool references but not full tool definitions
            "tools": [
                {
                    "tool_type": tool.get("tool_type"),
                    "url": tool.get("url"),
                    "workflow": {
                        "workflow_id": tool.get("workflow", {}).get("workflow_id"),
                        "approval": tool.get("workflow", {}).get("approval"),
                    },
                }
                for tool in config_dict.get("tools", [])
            ],
            "memory_enabled": config_dict.get("memory_enabled", False),
        }

        return config_dict

    async def session_exists(self, session_id: str) -> bool:
        """Check if a session exists"""
        key = f"session_data:{session_id}"
        return await self.redis.exists(key)

    async def get_session_data(
        self,
        session_id: str,
        chat_context: Optional[List[dict]] = None,
    ) -> Tuple[
        AgentConfig,
        str,
        Optional[ListMemory],
        Optional[CancellationToken],
        Optional[str],
        Optional[bool],
        Optional[dict],
    ]:
        """Get session data including agent config, communication type, memory, and cancellation token"""
        key = f"session_data:{session_id}"

        try:
            session_data = await self.redis.get_hash(key)

            if not session_data:
                raise ValueError(f"Session {session_id} not found")

            agent_config = session_data.get("agent_config")
            if isinstance(agent_config, str):
                agent_config = json.loads(agent_config)

            communication_type = session_data.get("communication_type", "single")

            # Get memory and cancellation token for this session
            memory = None
            cancellation_token = None

            async with self._lock:
                # Check if we already have memory for this session
                memory = self.session_memories.get(session_id)

                # Update ListMemory for compatibility
                if memory and chat_context:
                    for msg in chat_context:
                        await memory.add(
                            MemoryContent(
                                content=msg,
                                mime_type=MemoryMimeType.JSON,
                            )
                        )

                cancellation_token = self.cancellation_tokens.get(session_id)
                if not cancellation_token:
                    cancellation_token = CancellationToken()
                    self.cancellation_tokens[session_id] = cancellation_token

            # Extract additional fields
            organization_id = session_data.get("organization_id")
            use_knowledge = session_data.get("use_knowledge", False)
            variables = session_data.get("variables")
            user_id = session_data.get("user_id")

            logger.debug("Session data retrieved", extra={"session_id": session_id})
            return (
                agent_config,
                communication_type,
                memory,
                cancellation_token,
                organization_id,
                use_knowledge,
                variables,
                user_id,
            )

        except Exception as e:
            logger.error(
                "Failed to get session data",
                exc_info=True,
                extra={"session_id": session_id, "error": str(e)},
            )
            raise e

    async def update_session_memory(
        self, session_id: str, message: Dict[str, Any]
    ) -> None:
        """Add a message to the session memory and update Redis with compression and truncation"""

        # Update in-memory storage for both ListMemory and our message cache
        memory = None
        async with self._lock:
            # Update ListMemory for compatibility with existing code
            memory = self.session_memories.get(session_id)

        # Update ListMemory for compatibility
        if memory:
            message["current_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            await memory.add(
                MemoryContent(
                    content=message,
                    mime_type=MemoryMimeType.JSON,
                )
            )

    async def delete_session(self, session_id: str) -> bool:
        """
        Delete a session and clean up all associated resources.
        Returns True if deletion was successful, False otherwise.
        """
        try:
            # Check if session exists first
            if not await self.session_exists(session_id):
                logger.warning(
                    "Attempted to delete non-existent session",
                    extra={"session_id": session_id},
                )
                return False

            # Clean up memory resources
            async with self._lock:
                if session_id in self.session_memories:
                    memory = self.session_memories[session_id]
                    if memory:
                        await memory.close()
                    del self.session_memories[session_id]

                if session_id in self.cancellation_tokens:
                    # Cancel any ongoing operations
                    token = self.cancellation_tokens[session_id]
                    if token and not token.is_cancelled:
                        token.cancel()
                    del self.cancellation_tokens[session_id]

            # Remove all Redis keys associated with the session
            key = f"session_data:{session_id}"

            # Delete all keys
            await self.redis.delete(key)

            logger.info(
                "Session deleted successfully", extra={"session_id": session_id}
            )
            return True

        except Exception as e:
            logger.error(
                "Failed to delete session",
                exc_info=True,
                extra={"session_id": session_id, "error": str(e)},
            )
            return False

    async def end_session(self, session_id: str) -> None:
        """End a session and clean up resources"""
        key = f"session_data:{session_id}"

        # Clean up memory resources
        async with self._lock:
            if session_id in self.session_memories:
                memory = self.session_memories[session_id]
                if memory:
                    await memory.close()
                del self.session_memories[session_id]

            if session_id in self.cancellation_tokens:
                del self.cancellation_tokens[session_id]

        # Remove from Redis
        await self.redis.delete(key)

    async def get_active_sessions(
        self, user_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get all active sessions, optionally filtered by user_id"""
        # Use Redis SCAN for efficient session listing
        pattern = "session_data:*"
        session_keys = await self.redis.scan(pattern)

        active_sessions = []
        for key in session_keys:
            session_data = await self.redis.get_hash(key)
            # Filter by user_id if provided
            if user_id is None or session_data.get("user_id") == user_id:
                active_sessions.append(session_data)

        return active_sessions
