import asyncio
import json
import logging
from enum import Enum
from typing import Any, Dict, List, Optional, Union

import httpx
from autogen_core import CancellationToken
from autogen_core.tools import BaseTool
from autogen_ext.tools.mcp import (
    Mcp<PERSON>orkbench,
    SseMcp<PERSON>ool<PERSON><PERSON>pter,
    SseServerParams,
    mcp_server_tools,
)
from pydantic import BaseModel, create_model

from ..helper.api_call import AuthType, HttpMethods, HttpRequestHelper
from ..kafka_client.producer import kafka_producer
from ..services.mcp_service import MCPSseClientWrapper
from ..shared.config.base import get_settings
from ..utils.constants import SSEEventType
from ..utils.converter import to_valid_identifier

# Configure logger
logger = logging.getLogger(__name__)

# Map JSON Schema types to Python types
json_type_to_py = {
    "string": str,
    "integer": int,
    "number": float,
    "boolean": bool,
    "object": dict,
    "array": list,
}

mcp_execute_endpoint = "mcp-execute/server"
stream_endpoint = "mcp-execute/stream"


async def load_mcp_tool_workbench_from_schema(schema: dict):
    """
    Given a schema dict, return a list of MCP tool adapters (SseMcpToolAdapter or StdioMcpToolAdapter)
    using mcp_server_tools. This is the recommended way to get all tools for an agent's tools list.
    """
    if "mcp" in schema and "sse_url" in schema["mcp"]:
        server_params = SseServerParams(url=schema["mcp"]["sse_url"])

        print(f"Loading MCP tools from schema: {server_params}")
    else:
        raise ValueError("Schema must contain 'mcp.sse_url' for SSE MCP server.")
    return McpWorkbench(server_params)


async def load_mcp_tool_adapters_from_schema(schema: dict):
    """
    Given a schema dict, return a list of MCP tool adapters (SseMcpToolAdapter or StdioMcpToolAdapter)
    using mcp_server_tools. This is the recommended way to get all tools for an agent's tools list.
    """
    if "mcp" in schema and "sse_url" in schema["mcp"]:
        server_params = SseServerParams(url=schema["mcp"]["sse_url"])
        print(f"Loading MCP tools from schema: {server_params}")
    else:
        raise ValueError("Schema must contain 'mcp.sse_url' for SSE MCP server.")

    # Get the translation tool from the server
    return await SseMcpToolAdapter.from_server_params(server_params, "translation")


async def load_mcp_tool_from_schema(schema: dict):
    """
    Given a schema dict, return a list of MCP tool adapters (SseMcpToolAdapter or StdioMcpToolAdapter)
    using mcp_server_tools. This is the recommended way to get all tools for an agent's tools list.
    """
    if "mcp" in schema and "sse_url" in schema["mcp"]:
        server_params = SseServerParams(url=schema["mcp"]["sse_url"])
        print(f"Loading MCP tools from schema: {server_params}")
    else:
        raise ValueError("Schema must contain 'mcp.sse_url' for SSE MCP server.")
    return await mcp_server_tools(server_params)


async def load_all_mcp_tool_adapters_from_schema_list(schemas: list[dict]):
    """
    Given a list of schema dicts, return a flat list of all MCP tool adapters from all servers.
    This is the recommended entrypoint for building a full agent tools list.
    """
    all_tools = []
    for schema in schemas:
        adapters = await load_mcp_tool_from_schema(schema)
        print(f"Loaded tools from schema: {adapters}")
        all_tools.extend(adapters)
    return all_tools


class McpToolLoader:
    """
    Utility class to load mcps as dynamic tools and execute them.
    """

    def __init__(self):
        """
        Initialize the mcp tool loader.

        Args:
            mcp_api_base_url: Base URL for mcp execution API
            stream_api_base_url: Base URL for streaming results
            auth_token: Optional authentication token
            auth_type: Authentication type (default: BEARER)
        """
        self.settings = get_settings()
        logger.info("Initializing McpToolLoader")

        # Initialize HTTP request helper for API calls
        self.http_client = HttpRequestHelper(
            base_url=self.settings.workflow_api_gateway.api_url,
            auth_token=self.settings.workflow_api_gateway.api_key,
            auth_type=AuthType.API_KEY,
            api_key_name="X-Server-Auth-Key",
            timeout=60,  # Longer timeout for mcp operations
        )
        self.mcp_client = MCPSseClientWrapper()

        self.kafka_agent_response_topic = self.settings.kafka.kafka_agent_response_topic
        logger.debug(
            f"HTTP client initialized with base URL: {self.settings.workflow_api_gateway.api_url}"
        )

    async def create_mcp_tool_from_metadata(
        self, run_id: str, user_id: str, mcp_metadata: Dict[str, Any]
    ) -> BaseTool:
        """
        Create a dynamic tool from mcp metadata.

        Args:
            mcp_metadata: Mcp metadata from the API

        Returns:
            A BaseTool instance that can execute the mcp
        """

        # Extract the first SSE URL from the urls list

        mcp_tools_config = mcp_metadata.get("mcp_tools_config")

        if mcp_tools_config is None:
            logger.error("No mcp_tools_config found in mcp_metadata")
            raise ValueError("No mcp_tools_config found in mcp_metadata")

        tools = mcp_tools_config.get("tools")

        mcp_id = mcp_metadata.get("id")

        mcp_name = mcp_metadata.get("name")

        mcp_tools = []

        for tool in tools:
            name = to_valid_identifier(f"{mcp_name}_{tool.get('name')}")
            tool_name = tool.get("name")
            description = tool.get("description")
            json_schema = tool.get("input_schema")

            logger.info(f"Mcp tool schema for {name}: {json_schema}")

            # Create the dynamic tool with our execution endpoint
            mcp_tools.append(
                self._create_mcp_execution_tool(
                    run_id=run_id,
                    user_id=user_id,
                    mcp_id=mcp_id,
                    name=name,
                    tool_name=tool_name,
                    description=description,
                    json_schema=json_schema,
                )
            )

        return mcp_tools

    def _resolve_schema_references(
        self, schema: Dict[str, Any], definitions: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Resolve $ref references in JSON schema using $defs definitions.

        Args:
            schema: The schema object that may contain $ref
            definitions: The $defs definitions from the root schema

        Returns:
            Resolved schema object
        """
        if definitions is None:
            definitions = {}

        if isinstance(schema, dict):
            if "$ref" in schema:
                ref_path = schema["$ref"]
                if ref_path.startswith("#/$defs/"):
                    def_name = ref_path.replace("#/$defs/", "")
                    if def_name in definitions:
                        return self._resolve_schema_references(
                            definitions[def_name], definitions
                        )
                    else:
                        logger.warning(f"Definition {def_name} not found in $defs")
                        return schema
                else:
                    logger.warning(f"Unsupported $ref format: {ref_path}")
                    return schema
            else:
                # Recursively resolve references in nested objects
                resolved = {}
                for key, value in schema.items():
                    resolved[key] = self._resolve_schema_references(value, definitions)
                return resolved
        elif isinstance(schema, list):
            return [
                self._resolve_schema_references(item, definitions) for item in schema
            ]
        else:
            return schema

    def _handle_complex_type(
        self,
        property_schema: Dict[str, Any],
        field_name: str,
        definitions: Dict[str, Any] = None,
    ):
        """
        Handle complex JSON schema types including anyOf, enums, arrays, and objects.

        Args:
            property_schema: The property schema definition
            field_name: Name of the field
            definitions: Schema definitions for reference resolution

        Returns:
            Tuple of (python_type, default_value, field_info)
        """
        if definitions is None:
            definitions = {}

        # Resolve any $ref first
        resolved_schema = self._resolve_schema_references(property_schema, definitions)

        # Handle enum types
        if "enum" in resolved_schema:
            enum_values = resolved_schema["enum"]
            enum_name = f"{field_name.title()}Enum"
            # Create dynamic enum class
            enum_class = Enum(enum_name, {val: val for val in enum_values})
            return enum_class, None, None

        # Handle anyOf (union types, often used for optional fields)
        if "anyOf" in resolved_schema:
            any_of_schemas = resolved_schema["anyOf"]
            # Check if this is an optional field (has null type)
            has_null = any(schema.get("type") == "null" for schema in any_of_schemas)
            non_null_schemas = [
                schema for schema in any_of_schemas if schema.get("type") != "null"
            ]

            if has_null and len(non_null_schemas) == 1:
                # This is an optional field
                inner_type, _, _ = self._handle_complex_type(
                    non_null_schemas[0], field_name, definitions
                )
                return Optional[inner_type], None, None
            else:
                # Multiple non-null types - use Union
                types = []
                for schema in non_null_schemas:
                    inner_type, _, _ = self._handle_complex_type(
                        schema, field_name, definitions
                    )
                    types.append(inner_type)
                if has_null:
                    types.append(type(None))
                return Union[tuple(types)], None, None

        # Handle array types
        if resolved_schema.get("type") == "array":
            items_schema = resolved_schema.get("items", {})
            if items_schema:
                item_type, _, _ = self._handle_complex_type(
                    items_schema, f"{field_name}_item", definitions
                )
                return List[item_type], None, None
            else:
                return List[Any], None, None

        # Handle object types
        if resolved_schema.get("type") == "object":
            return dict, None, None

        # Handle basic types
        schema_type = resolved_schema.get("type", "string")
        python_type = json_type_to_py.get(schema_type, str)

        # Handle default values
        default_value = resolved_schema.get("default")
        if default_value is not None:
            return python_type, default_value, None

        return python_type, None, None

    def _create_mcp_execution_tool(
        self,
        run_id,
        user_id,
        mcp_id: str,
        name: str,
        tool_name: str,
        description: str,
        json_schema: Dict[str, Any],
    ) -> BaseTool:
        """
        Create a dynamic tool that executes a mcp.

        Args:
            mcp_id: mcp_id of the mcp to execute
            name: Name for the tool
            description: Description for the tool
            json_schema: JSON schema for the tool parameters

        Returns:
            A BaseTool instance
        """
        logger.debug(
            f"Creating execution tool for mcp {mcp_id} with name {name} tool name {tool_name}"
        )

        # Extract definitions from the schema
        definitions = json_schema.get("$defs", {})

        # Build fields dict with correct Python types
        fields = {}
        properties = json_schema.get("properties", {})
        required_fields = json_schema.get("required", [])

        for field_name, property_schema in properties.items():
            try:
                # Use the complex type handler for all fields
                py_type, default_value, field_info = self._handle_complex_type(
                    property_schema, field_name, definitions
                )

                # Determine if field is required
                if field_name in required_fields:
                    if default_value is not None:
                        fields[field_name] = (py_type, default_value)
                    else:
                        fields[field_name] = (py_type, ...)
                else:
                    # Optional field
                    if default_value is not None:
                        fields[field_name] = (py_type, default_value)
                    else:
                        fields[field_name] = (py_type, None)

                logger.debug(
                    f"Added field {field_name} with type {py_type} to tool schema"
                )

            except Exception as e:
                logger.warning(f"Error processing field {field_name}: {e}")
                # Fallback to string type
                default = ... if field_name in required_fields else None
                fields[field_name] = (str, default)

        ArgsModel = create_model(f"{name}_Args", **fields)
        ArgsModel.model_rebuild()  # Ensure the model is fully defined
        logger.debug(f"Created args model for {name}: {ArgsModel}")

        class McpTool(BaseTool[ArgsModel, BaseModel]):
            def __init__(self, loader: McpToolLoader):
                super().__init__(
                    args_type=ArgsModel,
                    return_type=BaseModel,
                    name=name,
                    description=description,
                )
                self.mcp_id = mcp_id
                self.run_id = run_id
                self.loader = loader
                self.user_id: str = user_id
                self.settings = get_settings()
                self.kafka_agent_response_topic = (
                    self.settings.kafka.kafka_agent_response_topic
                )
                logger.debug(f"Initialized McpTool for {mcp_id}")

            def _serialize_enum_values(self, data: Any) -> Any:
                """
                Recursively convert enum values to their string representations for JSON serialization.

                Args:
                    data: The data structure to process

                Returns:
                    Data structure with enum values converted to strings
                """
                if isinstance(data, Enum):
                    return data.value
                elif isinstance(data, dict):
                    return {
                        key: self._serialize_enum_values(value)
                        for key, value in data.items()
                    }
                elif isinstance(data, list):
                    return [self._serialize_enum_values(item) for item in data]
                elif isinstance(data, tuple):
                    return tuple(self._serialize_enum_values(item) for item in data)
                else:
                    return data

            async def run(self, args: ArgsModel, cancellation_token: CancellationToken):
                """
                Execute the mcp and return the result as a string.
                If the mcp returns a stream, join the stream and return the full result.
                If an error occurs, return a user-friendly error message.
                """
                args_dict = args.model_dump(exclude_none=True)
                logger.debug(f"Raw args_dict before enum serialization: {args_dict}")

                # Convert enum values to their string representations for JSON serialization
                args_dict = self._serialize_enum_values(args_dict)
                logger.debug(f"Serialized args_dict after enum conversion: {args_dict}")

                payload = {
                    "mcp_id": self.mcp_id,
                    "tool_name": tool_name,
                    "tool_parameters": args_dict,
                    "retries": 3,
                    "user_id": self.user_id,
                }
                logger.debug(f"Mcp execution payload: {payload}")

                headers = [
                    ("correlationId", self.run_id.encode("utf-8")),
                    (
                        "reply-topic",
                        self.kafka_agent_response_topic.encode("utf-8"),
                    ),
                ]

                await kafka_producer.init_kafka_producer()

                try:
                    logger.info(f"Sending execution request for mcp {self.mcp_id}")
                    response = await asyncio.to_thread(
                        self.loader.http_client.post,
                        mcp_execute_endpoint,
                        json_data=payload,
                    )
                    logger.debug(f"Execution response: {response}")

                    if isinstance(response, dict) and "request_id" in response:
                        request_id = response["request_id"]
                        logger.info(f"Got request ID from response: {request_id}")

                        resp = {
                            "run_id": self.run_id,
                            "request_id": request_id,
                            "message": f"Agent initiated {tool_name} mcp tool execution.",
                            "tool_name": tool_name,
                            "success": True,
                            "final": True,
                            "event_type": SSEEventType.MCP_EXECUTION_STARTED.value,
                        }
                        await kafka_producer.send_message(
                            self.kafka_agent_response_topic,
                            resp,
                            headers,
                        )

                    else:
                        logger.warning(f"No request ID in response: {response}")

                    # Stream the results
                    async def stream_results():
                        stream_url = f"{stream_endpoint}/{request_id}"
                        logger.info(f"Starting to stream results from: {stream_url}")

                        async with httpx.AsyncClient(
                            base_url=self.loader.http_client.base_url,
                            headers=self.loader.http_client.headers,
                            timeout=300,
                        ) as client:
                            try:
                                logger.info(
                                    f"Opening stream connection to {stream_url}"
                                )
                                async with client.stream(
                                    HttpMethods.GET.value, stream_url
                                ) as response:
                                    response.raise_for_status()
                                    logger.info(
                                        f"Stream connection established, status: {response.status_code}"
                                    )

                                    buffer = ""
                                    async for chunk in response.aiter_text():
                                        logger.info(
                                            f"Received stream chunk of size: {len(chunk)} bytes"
                                        )
                                        buffer += chunk
                                        while "\n\n" in buffer:
                                            message, buffer = buffer.split("\n\n", 1)
                                            for line in message.split("\n"):
                                                if line.startswith("data:"):
                                                    data = line[5:].strip()
                                                    try:
                                                        json_data = json.loads(data)
                                                        logger.info(
                                                            f"Parsed JSON data from stream: {json_data}"
                                                        )
                                                        yield json.dumps(json_data)
                                                    except json.JSONDecodeError:
                                                        logger.warning(
                                                            f"Failed to parse JSON from stream: {data}"
                                                        )
                                                        yield data
                            except Exception as e:
                                logger.error(
                                    f"Error streaming results: {str(e)}", exc_info=True
                                )
                                yield json.dumps(
                                    {"error": f"Streaming error: {str(e)}"}
                                )

                    # Join the stream and return the full result as a string
                    logger.info(f"Returning stream generator for mcp {self.mcp_id}")
                    result_chunks = []
                    async for chunk in stream_results():
                        result_chunks.append(chunk)
                    return "\n".join(result_chunks)

                except Exception as e:
                    logger.error(
                        f"Error executing mcp {self.mcp_id}: {str(e)}",
                        exc_info=True,
                    )
                    resp = {
                        "run_id": self.run_id,
                        "request_id": None,
                        "tool_name": tool_name,
                        "message": f"mcp tool execution error: {str(e)}",
                        "success": False,
                        "final": True,
                        "event_type": SSEEventType.MCP_EXECUTION_FAILED.value,
                    }
                    await kafka_producer.send_message(
                        self.kafka_agent_response_topic,
                        resp,
                        headers,
                    )

                    return f"Mcp execution error: {str(e)}"

            async def join_stream(self, value):
                """
                Helper to join all chunks from an async generator.
                If value is already a string, return as is.
                """
                logger.info(f"Joining stream for mcp {self.mcp_id}")
                if hasattr(value, "__aiter__"):
                    results = []
                    async for chunk in value:
                        logger.info(f"Appending chunk of size: {len(chunk)} bytes")
                        results.append(chunk)
                    joined = "\n".join(results)
                    logger.info(f"Joined stream, total size: {len(joined)} bytes")
                    return joined
                logger.info("Value is not a stream, returning as string")
                return str(value)

        logger.info(f"Created mcp tool: {tool_name}")
        return McpTool(self)

    async def load_mcps_as_tools(self, run_id, user_id: str, mcps) -> List[BaseTool]:
        """
        Load all available mcps as tools.

        Args:
            mcps_endpoint: API endpoint to get mcp metadata

        Returns:
            List of BaseTool instances for each mcp
        """
        try:
            logger.info(
                f"Loading mcps as tools, count: {len(mcps) if isinstance(mcps, list) else 'unknown'}"
            )

            if not isinstance(mcps, list):
                logger.error(f"Error: Expected list of mcps, got {type(mcps)}")
                return []

            # Create a tool for each mcp
            tools = []
            for mcp in mcps:
                try:
                    mcp_id = mcp.get("id", "unknown")
                    logger.info(f"Creating tool for mcp: {mcp_id}")
                    mcp_tools = await self.create_mcp_tool_from_metadata(
                        run_id, user_id, mcp
                    )
                    tools.extend(mcp_tools)
                    logger.info(f"Successfully created tool for mcp: {mcp_id}")
                except Exception as e:
                    logger.error(
                        f"Error creating tool for mcp {mcp.get('id', 'unknown')}: {str(e)}",
                        exc_info=True,
                    )

            logger.info(f"Successfully loaded {len(tools)} mcp tools")
            return tools

        except Exception as e:
            logger.error(f"Error loading mcps: {str(e)}", exc_info=True)
            return []
