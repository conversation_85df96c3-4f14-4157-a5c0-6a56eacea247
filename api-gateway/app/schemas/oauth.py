from typing import Optional, List
from pydantic import BaseModel, Field
from app.core.oauth_providers import OAuthProvider


class OAuthMetadata(BaseModel):
    """
    Represents the metadata passed via the state parameter during the OAuth flow.
    While not directly used as a request/response model in your current code,
    this represents the structured data encoded in the 'state' string.
    """

    user_id: str = Field(..., description="The ID of the user requesting authorization")
    tool_name: str = Field(..., description="The name of the tool (e.g., 'google_calendar')")
    provider: OAuthProvider = Field(
        ..., description="The OAuth provider (e.g., 'google', 'microsoft')"
    )
    scopes: List[str] = Field(default_factory=list, description="Requested OAuth scopes")


class OAuthCallbackResponse(BaseModel):
    """
    Response model for the OAuth callback endpoint upon successful authorization.
    """

    success: bool = Field(..., example=True)
    message: str = Field(..., example="OAuth flow completed successfully")
    user_id: str = Field(..., example="user_example_123")
    tool_name: str = Field(..., example="google_calendar")


class OAuthCredentialResponse(BaseModel):
    """
    Response model for retrieving stored OAuth credentials.
    """

    success: bool = Field(..., example=True)
    message: str = Field(..., example="OAuth credentials retrieved successfully")
    expires_in: int = Field(
        ..., example=3599, description="Duration in seconds for which the access token is valid"
    )
    scope: str = Field(
        ...,
        example="https://www.googleapis.com/auth/calendar",
        description="Scope of the access granted",
    )


class OAuthErrorResponse(BaseModel):
    """
    A generic error response model for OAuth operations.
    """

    success: bool = Field(default=False, example=False)
    message: str = Field(..., example="Authorization failed: access_denied")


class OAuthCredentialInfo(BaseModel):
    """
    Information about an OAuth credential without the actual tokens.
    """

    id: str = Field(..., example="550e8400-e29b-41d4-a716-446655440000")
    user_id: str = Field(..., example="user_123")
    tool_name: str = Field(..., example="google_calendar")
    created_at: str = Field(..., example="2023-01-01T12:00:00")
    updated_at: str = Field(..., example="2023-01-02T12:00:00")
    last_used_at: str = Field(..., example="2023-01-03T12:00:00")


class OAuthCredentialListResponse(BaseModel):
    """
    Response model for listing OAuth credentials.
    """

    success: bool = Field(..., example=True)
    message: str = Field(..., example="OAuth credentials retrieved successfully")
    credentials: list[OAuthCredentialInfo] = Field(...)


class OAuthCredentialDeleteResponse(BaseModel):
    """
    Response model for deleting an OAuth credential.
    """

    success: bool = Field(..., example=True)
    message: str = Field(..., example="OAuth credential deleted successfully")


class ServerOAuthCredentialResponse(BaseModel):
    """
    Response model for retrieving OAuth credentials using server authentication.
    Includes additional metadata about the credential.
    """

    success: bool = Field(..., example=True)
    message: str = Field(..., example="OAuth credentials retrieved successfully")
    user_id: str = Field(..., example="user_123")
    tool_name: str = Field(..., example="google_calendar")
    provider: str = Field(..., example="google", description="OAuth provider")
    access_token: str = Field(..., example="ya29.a0AfH6SMB...")
    refresh_token: Optional[str] = Field(None, example="1//0eXxyz...")
    token_type: str = Field(default="Bearer", example="Bearer")
    expires_in: int = Field(
        ..., example=3599, description="Duration in seconds for which the access token is valid"
    )
    scope: str = Field(
        ...,
        example="https://www.googleapis.com/auth/calendar",
        description="Scope of the access granted",
    )


class OAuthAuthorizeRequest(BaseModel):
    """
    Request model for initiating OAuth authorization flow.
    """

    provider: OAuthProvider = Field(..., description="OAuth provider to use")
    tool_name: str = Field(..., description="The name of the tool")
    user_id: str = Field(..., description="The user ID")
    scopes: Optional[List[str]] = Field(
        None, description="Custom scopes (optional, will use tool defaults if not provided)"
    )
    redirect_url: Optional[str] = Field(
        None, description="Optional redirect URL after OAuth completion"
    )


class OAuthProviderInfo(BaseModel):
    """
    Information about an OAuth provider.
    """

    provider: OAuthProvider = Field(..., description="Provider identifier")
    name: str = Field(..., description="Human-readable provider name")
    auth_url: str = Field(..., description="Authorization URL")
    supported_tools: List[str] = Field(..., description="List of supported tools")


class OAuthProvidersListResponse(BaseModel):
    """
    Response model for listing available OAuth providers.
    """

    success: bool = Field(..., example=True)
    message: str = Field(..., example="OAuth providers retrieved successfully")
    providers: List[OAuthProviderInfo] = Field(..., description="List of available providers")


class OAuthToolScopesResponse(BaseModel):
    """
    Response model for getting tool scope requirements.
    """

    success: bool = Field(..., example=True)
    message: str = Field(..., example="Tool scopes retrieved successfully")
    tool_name: str = Field(..., example="google_calendar")
    provider_scopes: dict = Field(..., description="Mapping of providers to required scopes")
